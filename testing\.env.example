# ATMA Testing Environment Configuration

# API Configuration
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000

# Test Configuration
TEST_TIMEOUT=30000
ASSESSMENT_TIMEOUT=300000
WEBSOCKET_TIMEOUT=10000

# Response Validation
SAVE_RESPONSES=false
EXPECTED_GATEWAY_HEADER=ATMA-API-Gateway
EXPECTED_GATEWAY_VERSION=1.0.0

# Admin Credentials (for admin testing)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Test Data Configuration
TEST_USER_EMAIL_DOMAIN=test.atma.local
TEST_USER_PASSWORD_PREFIX=TestPass123

# Logging Configuration
LOG_LEVEL=info
LOG_COLORS=true

# Rate Limiting Test Configuration
RATE_LIMIT_TEST_ENABLED=false
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# WebSocket Test Configuration
WS_CONNECTION_TIMEOUT=20000
WS_RECONNECTION_ATTEMPTS=5
WS_RECONNECTION_DELAY=1000

# Error Testing Configuration
ERROR_TESTING_ENABLED=true
INVALID_TOKEN_TESTING=true

# Performance Testing Configuration
PERFORMANCE_TESTING_ENABLED=false
CONCURRENT_USERS=5
TEST_DURATION_MINUTES=5

# Database Testing Configuration
DB_CLEANUP_ENABLED=true
CLEANUP_TEST_DATA=true

# Notification Testing Configuration
NOTIFICATION_VALIDATION_STRICT=true
NOTIFICATION_TIMEOUT=60000

# Security Testing Configuration
SECURITY_HEADER_VALIDATION=true
CORS_VALIDATION=true
SSL_VALIDATION=false

# Test Results Configuration
GENERATE_REPORTS=true
REPORT_FORMAT=json
REPORT_DIRECTORY=./results

# Mock Configuration
USE_MOCK_SERVICES=false
MOCK_ASSESSMENT_DELAY=5000
MOCK_NOTIFICATION_DELAY=1000
