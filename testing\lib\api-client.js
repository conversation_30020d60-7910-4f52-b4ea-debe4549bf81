const axios = require('axios');
const fs = require('fs');
const path = require('path');

class APIClient {
  constructor(baseURL, logger) {
    this.baseURL = baseURL || process.env.API_BASE_URL || 'http://localhost:3000/api';
    this.logger = logger;
    this.saveResponses = process.env.SAVE_RESPONSES === 'true';
    
    // Create responses directory if saving is enabled
    if (this.saveResponses) {
      const responsesDir = path.join(__dirname, '..', 'results', 'responses');
      if (!fs.existsSync(responsesDir)) {
        fs.mkdirSync(responsesDir, { recursive: true });
      }
      this.responsesDir = responsesDir;
    }

    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: parseInt(process.env.TEST_TIMEOUT) || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ATMA-E2E-Test-Client/1.0.0',
        'Accept': 'application/json'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`API Request: ${config.method.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          data: config.data
        });
        return config;
      },
      (error) => {
        this.logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging, validation, and saving
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`API Response: ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          headers: response.headers,
          data: response.data
        });

        // Validate API Gateway headers according to spec
        this._validateHeaders(response.headers);

        this._saveResponse(response);
        return response;
      },
      (error) => {
        if (error.response) {
          this.logger.error(`API Error Response: ${error.response.status} ${error.config.method.toUpperCase()} ${error.config.url}`, {
            status: error.response.status,
            headers: error.response.headers,
            data: error.response.data
          });

          // Validate headers even for error responses
          this._validateHeaders(error.response.headers);

          this._saveResponse(error.response, true);

          // Log API Gateway specific error format
          if (error.response.data && error.response.data.error) {
            this.logger.error(`API Gateway Error: ${error.response.data.error.code} - ${error.response.data.error.message}`);
          }
        } else {
          this.logger.error('API Network Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  _validateHeaders(headers) {
    try {
      // Validate API Gateway security headers according to spec
      const securityHeaderErrors = [];
      const expectedGateway = process.env.EXPECTED_GATEWAY_HEADER || 'ATMA-API-Gateway';
      const expectedVersion = process.env.EXPECTED_GATEWAY_VERSION || '1.0.0';

      if (!headers['x-gateway']) {
        securityHeaderErrors.push('Missing X-Gateway header');
      } else if (headers['x-gateway'] !== expectedGateway) {
        securityHeaderErrors.push(`Invalid X-Gateway header: expected "${expectedGateway}", got "${headers['x-gateway']}"`);
      }

      if (!headers['x-gateway-version']) {
        securityHeaderErrors.push('Missing X-Gateway-Version header');
      } else if (headers['x-gateway-version'] !== expectedVersion) {
        securityHeaderErrors.push(`Invalid X-Gateway-Version header: expected "${expectedVersion}", got "${headers['x-gateway-version']}"`);
      }

      if (!headers['x-request-id']) {
        securityHeaderErrors.push('Missing X-Request-ID header');
      } else if (!this._isValidUUID(headers['x-request-id'])) {
        securityHeaderErrors.push(`Invalid X-Request-ID header format: ${headers['x-request-id']}`);
      }

      // Validate rate limiting headers if present
      const rateLimitErrors = [];
      if (headers['x-ratelimit-limit']) {
        const limit = parseInt(headers['x-ratelimit-limit']);
        if (isNaN(limit) || limit < 0) {
          rateLimitErrors.push(`Invalid X-RateLimit-Limit header: ${headers['x-ratelimit-limit']}`);
        }
      }

      if (headers['x-ratelimit-remaining']) {
        const remaining = parseInt(headers['x-ratelimit-remaining']);
        if (isNaN(remaining) || remaining < 0) {
          rateLimitErrors.push(`Invalid X-RateLimit-Remaining header: ${headers['x-ratelimit-remaining']}`);
        }
      }

      if (headers['x-ratelimit-reset']) {
        const reset = parseInt(headers['x-ratelimit-reset']);
        if (isNaN(reset) || reset < 0) {
          rateLimitErrors.push(`Invalid X-RateLimit-Reset header: ${headers['x-ratelimit-reset']}`);
        }
      }

      // Validate CORS headers if present
      const corsErrors = [];
      if (headers['access-control-allow-origin'] && headers['access-control-allow-origin'] === 'null') {
        corsErrors.push('CORS Access-Control-Allow-Origin should not be null');
      }

      // Log validation results
      if (securityHeaderErrors.length > 0) {
        this.logger.warn('API Gateway security header validation failed:', securityHeaderErrors);
      } else {
        this.logger.debug('API Gateway security headers validated successfully');
      }

      if (rateLimitErrors.length > 0) {
        this.logger.warn('Rate limiting header validation failed:', rateLimitErrors);
      } else if (headers['x-ratelimit-limit']) {
        this.logger.debug('Rate limiting headers validated successfully');
      }

      if (corsErrors.length > 0) {
        this.logger.warn('CORS header validation warnings:', corsErrors);
      }

    } catch (error) {
      this.logger.warn('Header validation error:', error.message);
    }
  }

  _isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  _saveResponse(response, isError = false) {
    if (!this.saveResponses) return;

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const method = response.config.method.toUpperCase();
      const endpoint = response.config.url.replace(this.baseURL, '').replace(/\//g, '_');
      const status = response.status;

      const filename = `${timestamp}_${method}_${endpoint}_${status}${isError ? '_ERROR' : ''}.json`;
      const filepath = path.join(this.responsesDir, filename);

      const responseData = {
        timestamp: new Date().toISOString(),
        request: {
          method: response.config.method,
          url: response.config.url,
          headers: response.config.headers,
          data: response.config.data
        },
        response: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data
        },
        isError,
        headerValidation: {
          hasSecurityHeaders: !!(response.headers['x-gateway'] && response.headers['x-gateway-version'] && response.headers['x-request-id']),
          hasRateLimitHeaders: !!(response.headers['x-ratelimit-limit'] || response.headers['x-ratelimit-remaining'] || response.headers['x-ratelimit-reset'])
        }
      };

      fs.writeFileSync(filepath, JSON.stringify(responseData, null, 2));
    } catch (error) {
      this.logger.warn('Failed to save response:', error.message);
    }
  }

  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      this.logger.debug('Auth token set for API client');
    } else {
      delete this.client.defaults.headers.common['Authorization'];
      this.logger.debug('Auth token removed from API client');
    }
  }

  // Validate response according to API Gateway specification
  validateResponse(response, expectedType = null) {
    const errors = [];

    // Validate basic response structure
    if (!response.data) {
      errors.push('Response missing data object');
      return { isValid: false, errors };
    }

    const data = response.data;

    // Validate success field
    if (typeof data.success !== 'boolean') {
      errors.push('Response missing or invalid "success" field');
    }

    // For successful responses, validate timestamp
    if (data.success) {
      if (!data.timestamp || !this._isValidTimestamp(data.timestamp)) {
        errors.push('Response missing or invalid "timestamp" field');
      }
    } else {
      // For error responses, validate error structure
      if (!data.error) {
        errors.push('Error response missing "error" object');
      } else {
        if (!data.error.code) {
          errors.push('Error response missing "error.code" field');
        }
        if (!data.error.message) {
          errors.push('Error response missing "error.message" field');
        }
        if (!data.error.timestamp || !this._isValidTimestamp(data.error.timestamp)) {
          errors.push('Error response missing or invalid "error.timestamp" field');
        }
      }
    }

    // Log validation results
    if (errors.length > 0) {
      this.logger.warn('Response validation failed:', errors);
    } else {
      this.logger.debug('Response validation passed');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  _isValidTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date instanceof Date && !isNaN(date);
  }

  // Authentication endpoints
  async register(userData) {
    this.logger.info(`Registering user: ${userData.email}`);
    const response = await this.client.post('/auth/register', userData);
    return response.data;
  }

  async login(credentials) {
    this.logger.info(`Logging in user: ${credentials.email}`);
    const response = await this.client.post('/auth/login', credentials);
    return response.data;
  }

  async logout() {
    this.logger.info('Logging out user');
    const response = await this.client.post('/auth/logout');
    return response.data;
  }

  async getProfile() {
    this.logger.info('Getting user profile');
    const response = await this.client.get('/auth/profile');
    return response.data;
  }

  async updateProfile(profileData) {
    this.logger.info('Updating user profile');
    const response = await this.client.put('/auth/profile', profileData);
    return response.data;
  }

  async changePassword(passwordData) {
    this.logger.info('Changing user password');
    const response = await this.client.post('/auth/change-password', passwordData);
    return response.data;
  }

  async getTokenBalance() {
    this.logger.info('Getting token balance');
    const response = await this.client.get('/auth/token-balance');
    return response.data;
  }

  async getSchools(params = {}) {
    this.logger.info('Getting schools list');
    const response = await this.client.get('/auth/schools', { params });
    return response.data;
  }

  async createSchool(schoolData) {
    this.logger.info('Creating new school');
    const response = await this.client.post('/auth/schools', schoolData);
    return response.data;
  }

  // Assessment endpoints
  async submitAssessment(assessmentData, idempotencyKey = null) {
    this.logger.info('Submitting assessment');
    const headers = {};
    if (idempotencyKey) {
      headers['X-Idempotency-Key'] = idempotencyKey;
    }
    const response = await this.client.post('/assessment/submit', assessmentData, { headers });
    return response.data;
  }

  async getAssessmentStatus(jobId) {
    this.logger.debug(`Getting assessment status: ${jobId}`);
    const response = await this.client.get(`/assessment/status/${jobId}`);
    return response.data;
  }

  // Archive endpoints
  async getResults(params = {}) {
    this.logger.info('Getting assessment results');
    const response = await this.client.get('/archive/results', { params });
    return response.data;
  }

  async getResult(resultId) {
    this.logger.info(`Getting assessment result: ${resultId}`);
    const response = await this.client.get(`/archive/results/${resultId}`);
    return response.data;
  }

  async deleteResult(resultId) {
    this.logger.info(`Deleting assessment result: ${resultId}`);
    const response = await this.client.delete(`/archive/results/${resultId}`);
    return response.data;
  }

  async getJobs(params = {}) {
    this.logger.info('Getting assessment jobs');
    const response = await this.client.get('/archive/jobs', { params });
    return response.data;
  }

  async getJob(jobId) {
    this.logger.info(`Getting assessment job: ${jobId}`);
    const response = await this.client.get(`/archive/jobs/${jobId}`);
    return response.data;
  }

  async deleteJob(jobId) {
    this.logger.info(`Deleting assessment job: ${jobId}`);
    const response = await this.client.delete(`/archive/jobs/${jobId}`);
    return response.data;
  }

  async getJobStats() {
    this.logger.info('Getting job statistics');
    const response = await this.client.get('/archive/jobs/stats');
    return response.data;
  }

  async getArchiveStats(type = 'user', scope = 'overview', timeRange = '7 days') {
    this.logger.info(`Getting archive statistics: ${type}/${scope}`);
    const params = { type, scope, timeRange };
    const response = await this.client.get('/archive/v1/stats', { params });
    return response.data;
  }

  // Chatbot endpoints
  async createConversation(conversationData) {
    this.logger.info('Creating chatbot conversation');
    const response = await this.client.post('/chatbot/conversations', conversationData);
    return response.data;
  }

  async getConversations(params = {}) {
    this.logger.info('Getting chatbot conversations');
    const response = await this.client.get('/chatbot/conversations', { params });
    return response.data;
  }

  async getConversation(conversationId, params = {}) {
    this.logger.info(`Getting conversation: ${conversationId}`);
    const response = await this.client.get(`/chatbot/conversations/${conversationId}`, { params });
    return response.data;
  }

  async sendMessage(conversationId, messageData) {
    this.logger.info(`Sending message to conversation: ${conversationId}`);
    const response = await this.client.post(`/chatbot/conversations/${conversationId}/messages`, messageData);
    return response.data;
  }

  async getMessages(conversationId, params = {}) {
    this.logger.info(`Getting messages for conversation: ${conversationId}`);
    const response = await this.client.get(`/chatbot/conversations/${conversationId}/messages`, { params });
    return response.data;
  }

  async deleteConversation(conversationId) {
    this.logger.info(`Deleting conversation: ${conversationId}`);
    const response = await this.client.delete(`/chatbot/conversations/${conversationId}`);
    return response.data;
  }

  async createConversationFromAssessment(assessmentData) {
    this.logger.info('Creating conversation from assessment');
    const response = await this.client.post('/chatbot/conversations/from-assessment', assessmentData);
    return response.data;
  }

  async getConversationSuggestions(conversationId) {
    this.logger.info(`Getting conversation suggestions: ${conversationId}`);
    const response = await this.client.get(`/chatbot/conversations/${conversationId}/suggestions`);
    return response.data;
  }

  async autoInitializeChatbot() {
    this.logger.info('Auto-initializing chatbot conversation');
    const response = await this.client.post('/chatbot/auto-initialize');
    return response.data;
  }

  async checkAssessmentReady(userId) {
    this.logger.info(`Checking assessment readiness for user: ${userId}`);
    const response = await this.client.get(`/chatbot/assessment-ready/${userId}`);
    return response.data;
  }

  // Health check endpoints
  async healthCheck() {
    this.logger.debug('Performing health check');
    const response = await this.client.get('/health');
    return response.data;
  }

  async notificationHealthCheck() {
    this.logger.debug('Performing notification health check');
    const response = await this.client.get('/notifications/health');
    return response.data;
  }

  // Admin endpoints
  async adminLogin(credentials) {
    this.logger.info(`Admin login: ${credentials.username}`);
    const response = await this.client.post('/admin/login', credentials);
    return response.data;
  }

  async adminRegister(adminData) {
    this.logger.info(`Registering admin: ${adminData.username}`);
    const response = await this.client.post('/admin/register', adminData);
    return response.data;
  }

  async deleteUser(userId) {
    this.logger.info(`Deleting user: ${userId}`);
    const response = await this.client.delete(`/archive/admin/users/${userId}`);
    return response.data;
  }

  async updateUserTokenBalance(userId, balanceData) {
    this.logger.info(`Updating token balance for user: ${userId}`);
    const response = await this.client.put(`/archive/admin/users/${userId}/token-balance`, balanceData);
    return response.data;
  }

  // Archive batch endpoints
  async createResultsBatch(batchData) {
    this.logger.info('Creating results batch');
    const response = await this.client.post('/archive/results/batch', batchData);
    return response.data;
  }

  async batchOperation(operation, operationData) {
    this.logger.info(`Performing batch operation: ${operation}`);
    const response = await this.client.post(`/archive/v1/batch/${operation}`, operationData);
    return response.data;
  }

  async getArchiveData(type, params = {}) {
    this.logger.info(`Getting archive data: ${type}`);
    const response = await this.client.get(`/archive/v1/data/${type}`, { params });
    return response.data;
  }

  async getArchiveHealth(component = 'all') {
    this.logger.debug(`Getting archive health: ${component}`);
    const response = await this.client.get(`/archive/v1/health/${component}`);
    return response.data;
  }
}

module.exports = APIClient;
