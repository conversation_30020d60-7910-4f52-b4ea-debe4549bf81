# ATMA E2E Testing Suite

Comprehensive end-to-end testing suite untuk ATMA (AI-Driven Talent Mapping Assessment) backend services yang sepenuhnya sesuai dengan spesifikasi API Gateway dan WebSocket manual.

## Overview

Testing suite ini dirancang untuk menguji seluruh flow aplikasi ATMA dari registrasi user hingga analisis persona dan chatbot interaction. Suite ini menggunakan real API calls dan WebSocket connections untuk memastikan semua komponen bekerja dengan baik secara terintegrasi dan sesuai dengan spesifikasi resmi.

## Features

- **Single User E2E Test**: Test complete user journey
- **Dual User Test**: Test concurrent user scenarios
- **Comprehensive API Test**: Test all API endpoints according to specification
- **WebSocket Comprehensive Test**: Complete WebSocket testing according to manual
- **Compliance Testing**: Full compliance validation against API and WebSocket specifications
- **Response Validation**: Comprehensive API response validation per specification
- **Security Headers Validation**: API Gateway security headers validation
- **Error Format Validation**: API Gateway error response format validation
- **Pagination Validation**: Consistent pagination structure validation
- **Cleanup Management**: Automatic test data cleanup
- **Detailed Logging**: Comprehensive test execution logs
- **Result Archiving**: Test results and responses saved for analysis
- **Compliance Reporting**: Automated compliance reports with recommendations

## Test Scenarios

### 1. Single User E2E Test (`single-user-test.js`)
- User registration with token balance validation
- User login with JWT validation
- WebSocket connection via API Gateway
- Profile update with validation
- Assessment submission with idempotency key
- Real-time notification waiting with structure validation
- Persona profile retrieval with complete validation
- Chatbot conversation with assessment integration
- Comprehensive cleanup

### 2. Comprehensive API Test (`comprehensive-api-test.js`)
- All authentication endpoints (register, login, profile, schools)
- All assessment endpoints (submit, status, health)
- All archive endpoints (results, jobs, statistics, batch operations)
- All chatbot endpoints (conversations, messages, assessment integration)
- All admin endpoints (login, user management, token balance)
- All health check endpoints (main, notifications, archive)
- School management endpoints (create, list)
- Response structure validation per API specification
- Security headers validation (X-Gateway, X-Gateway-Version, X-Request-ID)
- Rate limiting headers validation
- Error response format validation
- Idempotency key testing

### 3. WebSocket Comprehensive Test (`websocket-comprehensive-test.js`)
- WebSocket connection via API Gateway (recommended)
- Authentication flow with timeout testing
- Notification structure validation per WebSocket manual
- Assessment flow with real-time notifications
- Connection management testing (reconnection, state tracking)
- Event validation (analysis-started, analysis-complete, analysis-failed)

### 4. Error Scenarios Test (`error-scenarios-test.js`)
- Authentication errors (invalid credentials, missing token, invalid token)
- Validation errors (invalid email, missing fields)
- Authorization errors (user accessing admin endpoints)
- Error response format validation per API Gateway specification
- Rate limiting testing (when applicable)
- Connection management and reconnection
- Error handling and edge cases

### 4. Dual User Test (`dual-user-test.js`)
- Concurrent user registration
- Parallel assessment submissions
- WebSocket notifications for multiple users
- Resource isolation testing
- Concurrent chatbot interactions

### 5. Header Validation Test (`header-validation-test.js`) ⭐ NEW
- API Gateway security headers validation (X-Gateway, X-Gateway-Version, X-Request-ID)
- Rate limiting headers validation (X-RateLimit-*)
- Response format consistency testing
- Error response format validation
- Compliance with API Gateway external specification

### 6. WebSocket Notification Validation Test (`websocket-notification-validation-test.js`) ⭐ NEW
- Notification structure validation per WebSocket manual
- Metadata validation (assessmentName, estimatedProcessingTime, processingTime, errorType)
- Timestamp validation and format checking
- Complete assessment flow validation
- Notification validation statistics and reporting

### 7. Complete Test Suite Runner (`run-all-tests.js`) ⭐ NEW
- Runs all test suites in sequence
- Provides comprehensive compliance assessment
- Critical vs non-critical test classification
- Detailed reporting and recommendations
- API Gateway & WebSocket specification compliance status

### 8. Legacy Tests (maintained for compatibility)
- `websocket-test.js`: Basic WebSocket testing
- `chatbot-test.js`: Basic chatbot testing

## Installation

```bash
cd testing
npm install
```

## Prerequisites

1. **Services Running**: Pastikan semua ATMA services berjalan:
   - API Gateway (port 3000) - **Primary entry point**
   - Auth Service (port 3001)
   - Assessment Service (port 3002)
   - Archive Service (port 3003)
   - Chatbot Service (port 3004)
   - Notification Service (port 3005)

2. **Database**: Database harus accessible dan dalam keadaan clean state

3. **Environment**: Set environment variables sesuai kebutuhan

## Configuration

Create `.env` file in testing directory:

```env
# API Configuration (Use API Gateway as primary entry point)
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000

# Test Configuration
TEST_TIMEOUT=30000
ASSESSMENT_TIMEOUT=300000
WEBSOCKET_TIMEOUT=10000

# Test Data (Updated to match API specification)
TEST_EMAIL_DOMAIN=test.atma.local
TEST_PASSWORD=TestPassword123!
DEFAULT_ASSESSMENT_NAME=AI-Driven Talent Mapping

# Logging
SAVE_RESPONSES=true
LOG_LEVEL=info
```

## Usage

### Run Complete Compliance Test Suite (Recommended) ⭐
```bash
# Run comprehensive compliance tests against API and WebSocket specifications
npm run test:compliance
```

### Run Specification Compliance Tests ⭐
```bash
# Test API Gateway specification compliance
node header-validation-test.js

# Test WebSocket specification compliance
node websocket-notification-validation-test.js

# Run all tests with specification compliance assessment
node run-all-tests.js
```

### Run All Tests
```bash
npm run test
```

### Run Individual Tests
```bash
# Single user E2E test
npm run test:single

# Comprehensive API test (NEW)
npm run test:api

# Comprehensive WebSocket test (NEW)
npm run test:websocket-comprehensive

# Dual user test
npm run test:dual

# Legacy tests
npm run test:websocket
npm run test:chatbot
```

### Run with Custom Configuration
```bash
# With custom timeout
ASSESSMENT_TIMEOUT=600000 npm run test:single

# With response saving
SAVE_RESPONSES=true npm run test:api

# With debug logging
LOG_LEVEL=debug npm run test:websocket-comprehensive
```

## Test Flow

### Complete E2E Flow
1. Generate random test users
2. Register both users
3. Login both users
4. Connect WebSocket for both users
5. Update profiles for both users
6. Submit assessments for both users
7. Wait for WebSocket notifications
8. Retrieve persona profiles
9. Test chatbot functionality
10. Delete test accounts

### Validation Points
- HTTP status codes
- Response body structure
- Required headers
- JWT token format
- WebSocket event format
- Assessment data validation
- Persona profile completeness

## File Structure

```
testing/
├── package.json              # Dependencies and scripts
├── .env                     # Configuration
├── README.md                # This file
├── test-runner.js           # Main test runner
├── single-user-test.js      # Single user test
├── dual-user-test.js        # Dual user test
├── lib/
│   ├── api-client.js        # HTTP API client
│   ├── websocket-client.js  # WebSocket client
│   ├── test-data.js         # Test data generators
│   ├── validators.js        # Response validators
│   └── logger.js            # Logging utilities
├── tests/
│   ├── auth-test.js         # Authentication tests
│   ├── profile-test.js      # Profile management tests
│   ├── assessment-test.js   # Assessment submission tests
│   ├── websocket-test.js    # WebSocket notification tests
│   ├── chatbot-test.js      # Chatbot functionality tests
│   └── cleanup-test.js      # Cleanup utilities
└── results/                 # Test results and logs
    ├── logs/               # Test execution logs
    └── responses/          # Saved API responses
```

## Requirements

- Node.js 18+
- ATMA Backend Services running
- API Gateway accessible at localhost:3000
- WebSocket service accessible at localhost:3000

## Examples

### Basic Usage Examples

```bash
# Install dependencies
cd testing
npm install

# Run complete E2E test with 2 users
npm test

# Run single user test
npm run test:single

# Run dual user test with parallel execution
npm run test:dual

# Test only WebSocket functionality
npm run test:websocket

# Test only chatbot functionality
npm run test:chatbot

# Clean up test users and old files
npm run test:cleanup
```

### Advanced Usage

```bash
# Run with custom timeout
ASSESSMENT_TIMEOUT=600000 npm test

# Run with debug logging
DEBUG_MODE=true LOG_LEVEL=debug npm test

# Run without saving responses
SAVE_RESPONSES=false npm test

# Run with custom API URL
API_BASE_URL=http://localhost:4000/api npm test
```

### Environment Variables

```env
# Required
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000

# Optional
TEST_TIMEOUT=30000                    # HTTP request timeout
ASSESSMENT_TIMEOUT=300000             # Assessment completion timeout
WEBSOCKET_TIMEOUT=10000               # WebSocket auth timeout
TEST_EMAIL_DOMAIN=test.atma.local     # Test email domain
TEST_PASSWORD=TestPassword123!        # Test user password
DEBUG_MODE=true                       # Enable debug logging
LOG_LEVEL=info                        # Log level (error, warn, info, debug)
SAVE_RESPONSES=true                   # Save API responses to files
DEFAULT_ASSESSMENT_NAME=E2E Test      # Default assessment name
```

## Test Scenarios

### Scenario 1: Complete E2E Flow
Tests the entire user journey from registration to account deletion:
- User registration with random email
- User login and JWT token validation
- WebSocket connection and authentication
- Profile update with validation
- Assessment submission and processing
- Real-time notification handling
- Persona profile retrieval and validation
- Chatbot conversation testing
- Resource cleanup and account deletion

### Scenario 2: Parallel User Testing
Tests system behavior with multiple concurrent users:
- Simultaneous user registration
- Parallel assessment submissions
- Concurrent WebSocket connections
- Real-time notification handling for multiple users
- Resource contention testing

### Scenario 3: WebSocket Stress Testing
Focuses on WebSocket functionality:
- Connection establishment and authentication
- Notification delivery and validation
- Reconnection handling
- Connection state management
- Error handling and recovery

### Scenario 4: Chatbot Integration Testing
Tests AI chatbot functionality:
- Conversation creation and management
- Message sending and response validation
- Assessment-based conversation initialization
- Context preservation and conversation history
- Error handling and edge cases

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure all ATMA services are running
   ```bash
   # Check if services are running
   curl http://localhost:3000/api/health
   ```

2. **Authentication Timeout**: Check JWT token validity
   ```bash
   # Verify token format in logs
   grep "JWT" testing/results/logs/*.log
   ```

3. **WebSocket Connection Failed**: Verify WebSocket service is running
   ```bash
   # Check WebSocket health
   curl http://localhost:3000/api/notifications/health
   ```

4. **Assessment Timeout**: Increase ASSESSMENT_TIMEOUT in .env
   ```env
   ASSESSMENT_TIMEOUT=600000  # 10 minutes
   ```

5. **Rate Limiting**: Reduce concurrent requests or increase rate limits
   ```env
   # Add delays between requests
   TEST_DELAY=1000
   ```

### Debug Mode

Enable debug mode in `.env`:
```env
DEBUG_MODE=true
LOG_LEVEL=debug
SAVE_RESPONSES=true
```

This will provide:
- Detailed logging of all requests and responses
- WebSocket event logging
- Saved API responses in `results/responses/`
- Comprehensive test execution logs

### Log Analysis

```bash
# View latest test log
tail -f testing/results/logs/test-*.log

# Search for errors
grep -i error testing/results/logs/*.log

# Find specific user actions
grep "<EMAIL>" testing/results/logs/*.log

# Check WebSocket events
grep "WebSocket" testing/results/logs/*.log
```
