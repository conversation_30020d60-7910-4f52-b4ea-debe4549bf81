#!/usr/bin/env node

/**
 * ATMA Compliance Test Runner
 * 
 * This script runs comprehensive tests to ensure the testing suite
 * is compliant with API Gateway External API Documentation and
 * WebSocket Manual specifications.
 */

require('dotenv').config();
const Logger = require('./lib/logger');
const ComprehensiveAPITest = require('./comprehensive-api-test');
const WebSocketTest = require('./websocket-test');
const HeaderValidationTest = require('./header-validation-test');
const fs = require('fs');
const path = require('path');

class ComplianceTestRunner {
  constructor() {
    this.logger = new Logger();
    this.results = {
      api: null,
      websocket: null,
      headers: null,
      overall: {
        passed: 0,
        failed: 0,
        total: 0,
        compliance: 0
      }
    };
  }

  async runCompliance() {
    this.logger.header('ATMA API & WebSocket Compliance Test Suite');
    this.logger.info('Testing compliance with API Gateway External API Documentation and WebSocket Manual');
    this.logger.separator();

    try {
      // Run API compliance tests
      await this.runAPIComplianceTests();

      // Run WebSocket compliance tests
      await this.runWebSocketComplianceTests();

      // Run Header validation tests
      await this.runHeaderValidationTests();

      // Generate compliance report
      this.generateComplianceReport();

      // Print summary
      this.printComplianceSummary();

    } catch (error) {
      this.logger.error('Compliance test suite failed:', error.message);
      process.exit(1);
    }
  }

  async runAPIComplianceTests() {
    this.logger.step(1, 3, 'Running API Compliance Tests');
    
    try {
      const apiTest = new ComprehensiveAPITest();
      await apiTest.runTest();
      
      this.results.api = {
        passed: apiTest.testResults.passed,
        failed: apiTest.testResults.failed,
        total: apiTest.testResults.total,
        errors: apiTest.testResults.errors
      };

      this.logger.success(`API Tests: ${this.results.api.passed}/${this.results.api.total} passed`);
      
    } catch (error) {
      this.logger.error('API compliance tests failed:', error.message);
      this.results.api = { passed: 0, failed: 1, total: 1, errors: [error.message] };
    }
  }

  async runWebSocketComplianceTests() {
    this.logger.step(2, 3, 'Running WebSocket Compliance Tests');
    
    try {
      const wsTest = new WebSocketTest();
      await wsTest.runTest();
      
      this.results.websocket = {
        passed: wsTest.testResults.passed,
        failed: wsTest.testResults.failed,
        total: wsTest.testResults.total,
        errors: wsTest.testResults.errors
      };

      this.logger.success(`WebSocket Tests: ${this.results.websocket.passed}/${this.results.websocket.total} passed`);
      
    } catch (error) {
      this.logger.error('WebSocket compliance tests failed:', error.message);
      this.results.websocket = { passed: 0, failed: 1, total: 1, errors: [error.message] };
    }
  }

  async runHeaderValidationTests() {
    this.logger.step(3, 3, 'Running Header Validation Tests');
    
    try {
      const headerTest = new HeaderValidationTest();
      await headerTest.runTest();
      
      this.results.headers = {
        passed: headerTest.testResults.passed,
        failed: headerTest.testResults.failed,
        total: headerTest.testResults.total,
        errors: headerTest.testResults.errors
      };

      this.logger.success(`Header Tests: ${this.results.headers.passed}/${this.results.headers.total} passed`);
      
    } catch (error) {
      this.logger.error('Header validation tests failed:', error.message);
      this.results.headers = { passed: 0, failed: 1, total: 1, errors: [error.message] };
    }
  }

  generateComplianceReport() {
    this.logger.info('Generating compliance report...');

    // Calculate overall results
    this.results.overall.passed = (this.results.api?.passed || 0) + 
                                  (this.results.websocket?.passed || 0) + 
                                  (this.results.headers?.passed || 0);
    
    this.results.overall.failed = (this.results.api?.failed || 0) + 
                                  (this.results.websocket?.failed || 0) + 
                                  (this.results.headers?.failed || 0);
    
    this.results.overall.total = (this.results.api?.total || 0) + 
                                 (this.results.websocket?.total || 0) + 
                                 (this.results.headers?.total || 0);

    this.results.overall.compliance = this.results.overall.total > 0 ? 
      ((this.results.overall.passed / this.results.overall.total) * 100).toFixed(2) : 0;

    // Generate detailed report
    const report = {
      timestamp: new Date().toISOString(),
      compliance: {
        percentage: this.results.overall.compliance,
        status: this.results.overall.compliance >= 95 ? 'COMPLIANT' : 
                this.results.overall.compliance >= 80 ? 'PARTIALLY_COMPLIANT' : 'NON_COMPLIANT'
      },
      summary: this.results.overall,
      details: {
        api_tests: this.results.api,
        websocket_tests: this.results.websocket,
        header_tests: this.results.headers
      },
      specifications: {
        api_gateway_external: 'api-gateway/api_external.md',
        websocket_manual: 'notification-service/WEBSOCKET_MANUAL.md'
      },
      recommendations: this.generateRecommendations()
    };

    // Save report
    const reportDir = path.join(__dirname, 'results');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportPath = path.join(reportDir, `compliance-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.logger.success(`Compliance report saved: ${reportPath}`);
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.results.overall.compliance < 100) {
      recommendations.push('Review failed test cases and update implementation to match specifications');
    }

    if (this.results.api?.failed > 0) {
      recommendations.push('API endpoints need to be updated to match api_external.md specification');
    }

    if (this.results.websocket?.failed > 0) {
      recommendations.push('WebSocket implementation needs to be updated to match WEBSOCKET_MANUAL.md specification');
    }

    if (this.results.headers?.failed > 0) {
      recommendations.push('HTTP headers need to be updated to match API Gateway security requirements');
    }

    if (recommendations.length === 0) {
      recommendations.push('All tests passed! The implementation is fully compliant with specifications.');
    }

    return recommendations;
  }

  printComplianceSummary() {
    this.logger.separator();
    this.logger.header('COMPLIANCE TEST SUMMARY');

    // Overall compliance status
    const status = this.results.overall.compliance >= 95 ? '✅ COMPLIANT' : 
                   this.results.overall.compliance >= 80 ? '⚠️  PARTIALLY COMPLIANT' : '❌ NON-COMPLIANT';
    
    this.logger.info(`Compliance Status: ${status} (${this.results.overall.compliance}%)`);
    this.logger.separator();

    // Detailed results
    const summary = {
      'Total Tests': this.results.overall.total,
      'Passed': this.results.overall.passed,
      'Failed': this.results.overall.failed,
      'Compliance Rate': `${this.results.overall.compliance}%`
    };

    this.logger.table(summary, 'Overall Results');

    // Individual test suite results
    if (this.results.api) {
      this.logger.info(`API Tests: ${this.results.api.passed}/${this.results.api.total} passed`);
    }
    if (this.results.websocket) {
      this.logger.info(`WebSocket Tests: ${this.results.websocket.passed}/${this.results.websocket.total} passed`);
    }
    if (this.results.headers) {
      this.logger.info(`Header Tests: ${this.results.headers.passed}/${this.results.headers.total} passed`);
    }

    // Errors summary
    const allErrors = [
      ...(this.results.api?.errors || []),
      ...(this.results.websocket?.errors || []),
      ...(this.results.headers?.errors || [])
    ];

    if (allErrors.length > 0) {
      this.logger.separator();
      this.logger.error('Issues Found:');
      allErrors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.results.overall.compliance >= 95) {
      this.logger.success('🎉 Testing suite is COMPLIANT with API and WebSocket specifications!');
    } else {
      this.logger.error(`❌ Testing suite needs improvements to achieve full compliance.`);
      this.logger.info('Please review the compliance report for detailed recommendations.');
    }
  }
}

// Run compliance tests if this file is executed directly
if (require.main === module) {
  const runner = new ComplianceTestRunner();
  runner.runCompliance().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ComplianceTestRunner;
