#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const ResponseValidator = require('./lib/validators');

class HeaderValidationTest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA API Gateway Header Validation Test');
    
    try {
      // Setup test user
      await this.setupTestUser();

      // Test 1: Security Headers Validation
      this.logger.step(1, 4, 'Testing API Gateway security headers');
      await this.testSecurityHeaders();

      // Test 2: Rate Limiting Headers
      this.logger.step(2, 4, 'Testing rate limiting headers');
      await this.testRateLimitingHeaders();

      // Test 3: Response Format Validation
      this.logger.step(3, 4, 'Testing response format consistency');
      await this.testResponseFormat();

      // Test 4: Error Response Format
      this.logger.step(4, 4, 'Testing error response format');
      await this.testErrorResponseFormat();

      this.printTestSummary();

    } catch (error) {
      this.logger.error('Header validation test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.info('Setting up test user...');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });
    
    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    this.api.setAuthToken(this.user.token);
    
    this.logger.success(`Test user created: ${this.user.email}`);
  }

  async testSecurityHeaders() {
    try {
      this.logger.info('Testing API Gateway security headers...');
      
      // Test multiple endpoints to ensure consistent headers
      const endpoints = [
        { method: 'GET', endpoint: '/auth/profile', name: 'Profile' },
        { method: 'GET', endpoint: '/auth/token-balance', name: 'Token Balance' },
        { method: 'GET', endpoint: '/health', name: 'Health Check' }
      ];

      for (const { method, endpoint, name } of endpoints) {
        await this.testEndpointHeaders(method, endpoint, name);
      }

      this.testResults.passed++;
      this.logger.success('Security headers validation passed');

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Security headers test failed: ${error.message}`);
      this.logger.error('Security headers test failed:', error.message);
    }
  }

  async testEndpointHeaders(method, endpoint, name) {
    this.logger.info(`Testing headers for ${name} (${method} ${endpoint})`);

    let response;
    if (method === 'GET') {
      response = await this.api.client.get(endpoint);
    }

    // Validate security headers
    const headerErrors = this.validator.validateSecurityHeaders(response.headers);
    if (headerErrors.length > 0) {
      throw new Error(`${name} security headers validation failed: ${headerErrors.join(', ')}`);
    }

    // Validate additional headers according to API Gateway spec
    this.validateAdditionalHeaders(response.headers, name);

    this.logger.success(`${name} headers validated successfully`);
  }

  validateAdditionalHeaders(headers, endpointName) {
    // Validate Content-Type for JSON responses
    if (headers['content-type'] && !headers['content-type'].includes('application/json')) {
      this.logger.warn(`${endpointName}: Expected JSON content-type, got ${headers['content-type']}`);
    }

    // Validate CORS headers if present
    if (headers['access-control-allow-origin']) {
      if (headers['access-control-allow-origin'] === 'null') {
        this.logger.warn(`${endpointName}: CORS Access-Control-Allow-Origin should not be null`);
      }
    }

    // Validate Cache-Control headers for appropriate endpoints
    if (endpointName.includes('Health') && headers['cache-control']) {
      if (!headers['cache-control'].includes('no-cache')) {
        this.logger.warn(`${endpointName}: Health endpoints should have no-cache directive`);
      }
    }

    // Validate Server header is not exposing sensitive information
    if (headers['server'] && headers['server'].toLowerCase().includes('version')) {
      this.logger.warn(`${endpointName}: Server header may be exposing version information`);
    }

    // Validate X-Powered-By header is not present (security best practice)
    if (headers['x-powered-by']) {
      this.logger.warn(`${endpointName}: X-Powered-By header should be removed for security`);
    }
  }

  async testRateLimitingHeaders() {
    try {
      this.logger.info('Testing rate limiting headers...');
      
      // Make multiple requests to potentially trigger rate limiting
      const requests = [];
      for (let i = 0; i < 5; i++) {
        requests.push(this.api.getProfile());
      }
      
      const responses = await Promise.all(requests);
      
      // Check if any response has rate limiting headers
      let hasRateLimitHeaders = false;
      for (const response of responses) {
        if (response.headers['x-ratelimit-limit']) {
          hasRateLimitHeaders = true;
          
          // Validate rate limiting headers
          const rateLimitErrors = this.validator.validateRateLimitHeaders(response.headers);
          if (rateLimitErrors.length > 0) {
            throw new Error(`Rate limiting headers validation failed: ${rateLimitErrors.join(', ')}`);
          }
          
          this.logger.info('Rate limiting headers found and validated', {
            limit: response.headers['x-ratelimit-limit'],
            remaining: response.headers['x-ratelimit-remaining'],
            reset: response.headers['x-ratelimit-reset']
          });
          break;
        }
      }
      
      if (!hasRateLimitHeaders) {
        this.logger.warn('No rate limiting headers found (may be normal for low request volume)');
      }

      this.testResults.passed++;
      this.logger.success('Rate limiting headers test completed');

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Rate limiting test failed: ${error.message}`);
      this.logger.error('Rate limiting test failed:', error.message);
    }
  }

  async testResponseFormat() {
    try {
      this.logger.info('Testing response format consistency...');
      
      // Test various successful endpoints
      const responses = [
        await this.api.getProfile(),
        await this.api.getTokenBalance(),
        await this.api.healthCheck()
      ];

      for (const response of responses) {
        const validation = this.api.validateResponse(response);
        if (!validation.isValid) {
          throw new Error(`Response format validation failed: ${validation.errors.join(', ')}`);
        }
      }

      this.testResults.passed++;
      this.logger.success('Response format validation passed');

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Response format test failed: ${error.message}`);
      this.logger.error('Response format test failed:', error.message);
    }
  }

  async testErrorResponseFormat() {
    try {
      this.logger.info('Testing error response format...');
      
      // Remove auth token to trigger 401 error
      this.api.setAuthToken(null);
      
      try {
        await this.api.getProfile();
        throw new Error('Expected 401 error but request succeeded');
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // Validate error response format
          const validation = this.api.validateResponse(error.response);
          if (!validation.isValid) {
            throw new Error(`Error response format validation failed: ${validation.errors.join(', ')}`);
          }
          
          // Validate error structure according to API spec
          const errorData = error.response.data;
          if (!errorData.error || !errorData.error.code || !errorData.error.message) {
            throw new Error('Error response missing required error structure');
          }
          
          this.logger.success('Error response format validated successfully');
        } else {
          throw new Error(`Expected 401 error but got: ${error.message}`);
        }
      }

      // Restore auth token
      this.api.setAuthToken(this.user.token);

      this.testResults.passed++;
      this.logger.success('Error response format validation passed');

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Error response format test failed: ${error.message}`);
      this.logger.error('Error response format test failed:', error.message);
    }
  }

  printTestSummary() {
    this.testResults.total = this.testResults.passed + this.testResults.failed;
    
    this.logger.header('Header Validation Test Summary');
    this.logger.info(`Total Tests: ${this.testResults.total}`);
    this.logger.success(`Passed: ${this.testResults.passed}`);
    
    if (this.testResults.failed > 0) {
      this.logger.error(`Failed: ${this.testResults.failed}`);
      this.logger.error('Errors:');
      this.testResults.errors.forEach(error => {
        this.logger.error(`  - ${error}`);
      });
    }
    
    const successRate = this.testResults.total > 0 ? 
      ((this.testResults.passed / this.testResults.total) * 100).toFixed(1) : 0;
    this.logger.info(`Success Rate: ${successRate}%`);
  }

  async cleanup() {
    try {
      if (this.user && this.user.id) {
        this.logger.info('Cleaning up test user...');
        // Note: In a real scenario, you might want to delete the test user
        // For now, we'll just log out
        await this.api.logout();
      }
    } catch (error) {
      this.logger.warn('Cleanup failed:', error.message);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new HeaderValidationTest();
  test.runTest()
    .then(() => {
      process.exit(test.testResults.failed > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = HeaderValidationTest;
