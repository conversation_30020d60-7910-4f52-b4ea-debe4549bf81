#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const ResponseValidator = require('./lib/validators');

class ComprehensiveAPITest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA Comprehensive API Test - All Endpoints');
    
    try {
      // Setup test user
      await this.setupTestUser();

      // Test Authentication endpoints
      await this.testAuthenticationEndpoints();

      // Test Assessment endpoints
      await this.testAssessmentEndpoints();

      // Test Archive endpoints
      await this.testArchiveEndpoints();

      // Test Chatbot endpoints
      await this.testChatbotEndpoints();

      // Test Admin endpoints
      await this.testAdminEndpoints();

      // Test School endpoints
      await this.testSchoolEndpoints();

      // Test Health endpoints
      await this.testHealthEndpoints();

      // Test Error scenarios
      await this.testErrorScenarios();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.step(1, 6, 'Setting up test user');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });

    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    this.api.setAuthToken(this.user.token);
    
    this.logger.success('Test user setup completed');
  }

  async testAuthenticationEndpoints() {
    this.logger.step(2, 6, 'Testing Authentication endpoints');
    
    try {
      // Test profile retrieval
      await this.testEndpoint('GET /auth/profile', async () => {
        const response = await this.api.getProfile();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test profile update
      await this.testEndpoint('PUT /auth/profile', async () => {
        const response = await this.api.updateProfile(this.user.profileData);
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test password change
      await this.testEndpoint('POST /auth/change-password', async () => {
        const response = await this.api.changePassword({
          currentPassword: this.user.password,
          newPassword: this.user.password + '2'
        });
        this.validator.validate(response, 'auth');
        
        // Change back to original password
        await this.api.changePassword({
          currentPassword: this.user.password + '2',
          newPassword: this.user.password
        });
        
        return response;
      });

      // Test token balance
      await this.testEndpoint('GET /auth/token-balance', async () => {
        const response = await this.api.getTokenBalance();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test schools endpoints
      await this.testEndpoint('GET /auth/schools', async () => {
        const response = await this.api.getSchools({ limit: 5 });
        this.validator.validate(response, 'auth');
        
        // Validate pagination structure
        if (response.data.pagination) {
          const paginationErrors = this.validator.validatePaginationStructure(response.data.pagination);
          if (paginationErrors.length > 0) {
            throw new Error(`Pagination validation failed: ${paginationErrors.join(', ')}`);
          }
        }
        
        return response;
      });

      this.logger.success('Authentication endpoints test completed');
      
    } catch (error) {
      this.logger.error('Authentication endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Auth endpoints: ${error.message}`);
    }
  }

  async testAssessmentEndpoints() {
    this.logger.step(3, 6, 'Testing Assessment endpoints');
    
    try {
      // Submit assessment
      const submitResponse = await this.testEndpoint('POST /assessment/submit', async () => {
        const response = await this.api.submitAssessment(this.user.assessmentData);
        this.validator.validate(response, 'assessment-submission');
        return response;
      });

      this.user.jobId = submitResponse.data.jobId;

      // Test idempotency key
      await this.testEndpoint('POST /assessment/submit (with idempotency)', async () => {
        const idempotencyKey = `test-${Date.now()}-${Math.random().toString(36).substring(2)}`;
        const response = await this.api.submitAssessment(this.user.assessmentData, idempotencyKey);
        this.validator.validate(response, 'assessment-submission');
        return response;
      });

      // Check assessment status
      await this.testEndpoint('GET /assessment/status/:jobId', async () => {
        const response = await this.api.getAssessmentStatus(this.user.jobId);
        this.validator.validate(response, 'assessment-submission');
        return response;
      });

      // Test health endpoints
      await this.testEndpoint('GET /assessment/health', async () => {
        const response = await this.api.healthCheck();
        return response;
      });

      this.logger.success('Assessment endpoints test completed');
      
    } catch (error) {
      this.logger.error('Assessment endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Assessment endpoints: ${error.message}`);
    }
  }

  async testArchiveEndpoints() {
    this.logger.step(4, 6, 'Testing Archive endpoints');
    
    try {
      // Test results endpoints
      await this.testEndpoint('GET /archive/results', async () => {
        const response = await this.api.getResults({ limit: 5 });
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test jobs endpoints
      await this.testEndpoint('GET /archive/jobs', async () => {
        const response = await this.api.getJobs({ limit: 5 });
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test job statistics
      await this.testEndpoint('GET /archive/jobs/stats', async () => {
        const response = await this.api.getJobStats();
        this.validator.validate(response, 'auth');
        return response;
      });

      // Test archive statistics
      await this.testEndpoint('GET /archive/v1/stats', async () => {
        const response = await this.api.getArchiveStats('user', 'overview');
        this.validator.validate(response, 'auth');
        return response;
      });

      this.logger.success('Archive endpoints test completed');
      
    } catch (error) {
      this.logger.error('Archive endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Archive endpoints: ${error.message}`);
    }
  }

  async testChatbotEndpoints() {
    this.logger.step(5, 6, 'Testing Chatbot endpoints');
    
    try {
      // Test conversations list
      await this.testEndpoint('GET /chatbot/conversations', async () => {
        const response = await this.api.getConversations({ limit: 5 });
        this.validator.validate(response, 'chatbot');
        return response;
      });

      // Test assessment readiness check
      await this.testEndpoint('GET /chatbot/assessment-ready/:userId', async () => {
        const response = await this.api.checkAssessmentReady(this.user.id);
        this.validator.validate(response, 'chatbot');
        return response;
      });

      this.logger.success('Chatbot endpoints test completed');
      
    } catch (error) {
      this.logger.error('Chatbot endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Chatbot endpoints: ${error.message}`);
    }
  }

  async testAdminEndpoints() {
    this.logger.step(6, 8, 'Testing Admin endpoints');

    try {
      // Generate admin test data
      const adminData = this.testData.generateAdminData();
      let adminToken = null;

      // Test admin registration (requires existing admin token)
      // Skip if no admin token available
      this.logger.info('Skipping admin registration test (requires existing admin)');

      // Test admin login with default admin credentials
      try {
        await this.testEndpoint('POST /admin/login', async () => {
          const response = await this.api.adminLogin({
            username: process.env.ADMIN_USERNAME || 'admin',
            password: process.env.ADMIN_PASSWORD || 'admin123'
          });
          this.validator.validate(response, 'admin');
          adminToken = response.data.token;
          return response;
        });
      } catch (error) {
        this.logger.warn('Admin login test skipped - no admin credentials available');
      }

      // Test admin operations if we have admin token
      if (adminToken) {
        const originalToken = this.api.client.defaults.headers.common['Authorization'];
        this.api.setAuthToken(adminToken);

        // Test user token balance update
        await this.testEndpoint('PUT /admin/users/:userId/token-balance', async () => {
          const balanceData = this.testData.generateTokenBalanceData();
          const response = await this.api.updateUserTokenBalance(this.user.id, balanceData);
          this.validator.validate(response, 'auth');
          return response;
        });

        // Restore original token
        this.api.setAuthToken(this.user.token);
      }

      this.logger.success('Admin endpoints test completed');

    } catch (error) {
      this.logger.error('Admin endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Admin endpoints: ${error.message}`);
    }
  }

  async testSchoolEndpoints() {
    this.logger.step(7, 8, 'Testing School endpoints');

    try {
      // Test school creation
      await this.testEndpoint('POST /auth/schools', async () => {
        const schoolData = this.testData.generateSchoolData();
        const response = await this.api.createSchool(schoolData);
        this.validator.validate(response, 'school');
        return response;
      });

      this.logger.success('School endpoints test completed');

    } catch (error) {
      this.logger.error('School endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`School endpoints: ${error.message}`);
    }
  }

  async testHealthEndpoints() {
    this.logger.step(8, 9, 'Testing Health endpoints');

    try {
      // Test main health check
      await this.testEndpoint('GET /health', async () => {
        const response = await this.api.healthCheck();
        return response;
      });

      // Test notification health check
      await this.testEndpoint('GET /notifications/health', async () => {
        const response = await this.api.notificationHealthCheck();
        return response;
      });

      // Test archive health check
      await this.testEndpoint('GET /archive/v1/health', async () => {
        const response = await this.api.getArchiveHealth();
        return response;
      });

      this.logger.success('Health endpoints test completed');

    } catch (error) {
      this.logger.error('Health endpoints test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Health endpoints: ${error.message}`);
    }
  }

  async testErrorScenarios() {
    this.logger.step(9, 9, 'Testing Error scenarios');

    try {
      // Test unauthorized access
      await this.testEndpoint('GET /auth/profile (unauthorized)', async () => {
        const originalToken = this.api.client.defaults.headers.common['Authorization'];
        this.api.setAuthToken(null);

        try {
          await this.api.getProfile();
          throw new Error('Expected unauthorized error but request succeeded');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            // Validate error response structure
            const errorData = error.response.data;
            if (!errorData.success || errorData.success !== false) {
              throw new Error('Error response missing or invalid success field');
            }
            if (!errorData.error || !errorData.error.code || !errorData.error.message) {
              throw new Error('Error response missing proper error structure');
            }
            this.logger.info('Unauthorized error response validated successfully');
            return errorData;
          } else {
            throw error;
          }
        } finally {
          this.api.client.defaults.headers.common['Authorization'] = originalToken;
        }
      });

      // Test invalid data submission
      await this.testEndpoint('POST /assessment/submit (invalid data)', async () => {
        try {
          await this.api.submitAssessment({ invalid: 'data' });
          throw new Error('Expected validation error but request succeeded');
        } catch (error) {
          if (error.response && error.response.status >= 400 && error.response.status < 500) {
            // Validate error response structure
            const errorData = error.response.data;
            if (!errorData.success || errorData.success !== false) {
              throw new Error('Error response missing or invalid success field');
            }
            if (!errorData.error) {
              throw new Error('Error response missing error object');
            }
            this.logger.info('Validation error response validated successfully');
            return errorData;
          } else {
            throw error;
          }
        }
      });

      // Test non-existent resource
      await this.testEndpoint('GET /archive/results/non-existent-id', async () => {
        try {
          await this.api.getResult('non-existent-id');
          throw new Error('Expected not found error but request succeeded');
        } catch (error) {
          if (error.response && error.response.status === 404) {
            // Validate error response structure
            const errorData = error.response.data;
            if (!errorData.success || errorData.success !== false) {
              throw new Error('Error response missing or invalid success field');
            }
            if (!errorData.error || errorData.error.code !== 'NOT_FOUND') {
              throw new Error('Error response missing proper NOT_FOUND error code');
            }
            this.logger.info('Not found error response validated successfully');
            return errorData;
          } else {
            throw error;
          }
        }
      });

      this.logger.success('Error scenarios test completed');

    } catch (error) {
      this.logger.error('Error scenarios test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Error scenarios: ${error.message}`);
    }
  }

  async testEndpoint(name, testFunction, validateHeaders = true) {
    this.testResults.total++;

    try {
      this.logger.info(`Testing ${name}`);
      const response = await testFunction();

      // Validate common response structure
      if (response && typeof response === 'object') {
        if (response.success === undefined) {
          this.logger.warn(`${name}: Response missing 'success' field`);
        }
        if (response.success && !response.timestamp) {
          this.logger.warn(`${name}: Successful response missing 'timestamp' field`);
        }
      }

      // Validate headers if enabled and available
      if (validateHeaders && response && response.headers) {
        const headerErrors = this.validator.validateSecurityHeaders(response.headers);
        if (headerErrors.length > 0) {
          this.logger.warn(`${name}: Header validation warnings: ${headerErrors.join(', ')}`);
        }

        const rateLimitErrors = this.validator.validateRateLimitHeaders(response.headers);
        if (rateLimitErrors.length > 0) {
          this.logger.warn(`${name}: Rate limit header warnings: ${rateLimitErrors.join(', ')}`);
        }
      }

      this.logger.success(`${name}: PASSED`);
      this.testResults.passed++;
      return response;

    } catch (error) {
      this.logger.error(`${name}: FAILED - ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push(`${name}: ${error.message}`);
      throw error;
    }
  }

  async cleanup() {
    this.logger.info('Performing cleanup...');
    
    try {
      if (this.user && this.user.token) {
        this.api.setAuthToken(this.user.token);
        
        // Logout user
        try {
          await this.api.logout();
          this.logger.info('User logged out');
        } catch (error) {
          this.logger.warn('Failed to logout user:', error.message);
        }
      }

      this.api.setAuthToken(null);
      this.logger.success('Cleanup completed');
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
    }
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('COMPREHENSIVE API TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All API endpoints tested successfully!');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new ComprehensiveAPITest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ComprehensiveAPITest;
